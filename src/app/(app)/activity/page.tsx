'use client';

import type { DocumentSnapshot } from 'firebase/firestore';
import { useCallback, useEffect, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';

import { getActivityOrders } from '@/api/orders-api';
import { CollectionSelect } from '@/components/ui/collection-select';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { OrderEntity } from '@/constants/core.constants';
import { LocalStorageKeys } from '@/constants/storage.constants';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useRootContext } from '@/root-context';

import { ActivityTable } from './activity-table';

export default function ActivityPage() {
  const { collections } = useRootContext();
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [lastDoc, setLastDoc] = useState<DocumentSnapshot | null>(null);
  const [selectedCollection, setSelectedCollection] = useState('');
  const [sortBy, setSortBy] = useState<
    'date_desc' | 'price_desc' | 'price_asc'
  >('date_desc');
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  const loadOrders = useCallback(
    async (isLoadMore = false, currentLastDoc?: DocumentSnapshot | null) => {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setOrders([]);
        setLastDoc(null);
      }

      try {
        const filters = {
          collectionId: selectedCollection || undefined,
          sortBy: sortBy,
          limit: 20,
          lastDoc: isLoadMore ? currentLastDoc : null,
        };

        const result = await getActivityOrders(filters);

        if (isLoadMore) {
          setOrders((prev) => {
            // Deduplicate orders by ID
            const existingIds = new Set(prev.map((order) => order.id));
            const newOrders = result.orders.filter(
              (order) => !existingIds.has(order.id),
            );
            return [...prev, ...newOrders];
          });
        } else {
          setOrders(result.orders);
        }

        setLastDoc(result.lastDoc);
        setHasMore(result.hasMore);
      } catch (error) {
        console.error('Error loading activity orders:', error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [selectedCollection, sortBy],
  );

  const loadMoreOrders = useCallback(() => {
    if (!loadingMore && hasMore) {
      loadOrders(true, lastDoc);
    }
  }, [loadOrders, loadingMore, hasMore, lastDoc]);

  const containerRef = useInfiniteScroll({
    hasMore,
    loading: loadingMore,
    onLoadMore: loadMoreOrders,
  });

  useEffect(() => {
    loadOrders();
  }, [selectedCollection, sortBy, loadOrders]);

  return (
    <div className="space-y-4 pb-[75px]" ref={containerRef}>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#f5f5f5]">Activity</h1>
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="w-full max-w-sm">
            <CollectionSelect
              animated={isAnimatedCollection}
              collections={collections}
              value={selectedCollection}
              onValueChange={setSelectedCollection}
              placeholder="All Collections"
            />
          </div>

          <div className="w-full max-w-sm">
            <Select
              value={sortBy}
              onValueChange={(
                value: 'date_desc' | 'price_desc' | 'price_asc',
              ) => setSortBy(value)}
            >
              <SelectTrigger className="h-9 bg-transparent text-[var(--tgui--secondary_hint_color)] hover:bg-[var(--tgui--bg_color)] rounded-[14px] shadow-[0_0_0_2px_var(--tgui--outline)] border-transparent">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date_desc">Latest First</SelectItem>
                <SelectItem value="price_desc">Price: High to Low</SelectItem>
                <SelectItem value="price_asc">Price: Low to High</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <ActivityTable
          orders={orders}
          loading={loading}
          loadingMore={loadingMore}
          collections={collections}
        />
      </div>
    </div>
  );
}
